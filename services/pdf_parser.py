"""
PDF解析服务
使用Coze API将PDF文件转换为Markdown格式
"""

import json
import re
import httpx
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from core.logging import get_logger
from config import settings
import os
import re
import uuid
from pathlib import Path


logger = get_logger(__name__)


class PDFParseResult(BaseModel):
    """PDF解析结果"""
    markdown_content: str
    parsed_array: List[Dict[str, Any]]
    possible_titles: List[Dict[str, Any]]


class CozePDFService:
    """Coze PDF解析服务"""
    
    def __init__(self):
        self.upload_url = settings.coze_pdf.upload_url
        self.workflow_url = settings.coze_pdf.workflow_url
        self.api_token = settings.coze_pdf.api_token
        self.workflow_id = settings.coze_pdf.workflow_id

    async def upload_file(self, file_path: str) -> str:
        """
        上传PDF文件到Coze
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            file_id: 上传后的文件ID
        """
        try:
            with open(file_path, "rb") as f:
                files = {"file": f}
                headers = {
                    "Authorization": f"Bearer {self.api_token}"
                }
                
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(
                        self.upload_url,
                        headers=headers,
                        files=files
                    )
                    response.raise_for_status()
                    
                    result = response.json()
                    if result.get("code") == 0:
                        file_id = result["data"]["id"]
                        logger.info(f"文件上传成功，file_id: {file_id}")
                        return file_id
                    else:
                        raise Exception(f"文件上传失败: {result.get('msg', '未知错误')}")
                        
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise
    
    async def parse_pdf_stream(self, file_id: str) -> PDFParseResult:
        """
        调用Coze工作流解析PDF（流式响应）
        
        Args:
            file_id: 上传文件的ID
        
        Returns:
            PDFParseResult: 解析结果
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json"
            }
            
            data = {
                "workflow_id": self.workflow_id,
                "parameters": {
                    "UserFile": json.dumps({"file_id": file_id})
                }
            }
            
            # 收集流式响应
            all_data = []
            debug_url = None
            parsed_result = None
            logger.info(f"开始解析工作流响应，file_id: {file_id}")
            
            current_event_type = None
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                async with client.stream(
                    "POST",
                    self.workflow_url,
                    headers=headers,
                    json=data
                ) as response:
                    response.raise_for_status()
                    logger.info(f"工作流请求成功，状态码: {response.status_code}")
                    
                    # 解析SSE流
                    line_count = 0
                    async for line in response.aiter_lines():
                        line_count += 1
                        logger.debug(f"收到第{line_count}行数据: {line[:100]}...")
                        
                        if line.startswith("event: "):
                            current_event_type = line[6:].strip()
                            logger.info(f"收到事件类型: {current_event_type}")
                        elif line.startswith("data: "):
                            data_str = line[6:]  # 移除 "data: " 前缀
                            if data_str:
                                try:
                                    event_data = json.loads(data_str)
                                    all_data.append(event_data)
                                    
                                    # 使用当前事件类型
                                    event_type = current_event_type or event_data.get("event", "unknown")
                                    
                                    # 查找Message事件
                                    if event_type == "Message" and parsed_result is None:
                                        content = event_data.get("content", "")
                                        logger.info(f"收到Message事件，内容长度: {len(content)}")
                                        if content:
                                            try:
                                                # 解析content中的JSON
                                                parsed_content = json.loads(content)
                                                logger.info("成功解析Message内容")
                                                # 暂存解析结果，但不立即返回
                                                parsed_result = self._process_workflow_result(parsed_content)
                                            except json.JSONDecodeError as e:
                                                logger.error(f"解析Message内容失败: {str(e)}, 内容: {content[:100]}...")
                                    elif event_type == "Done":
                                        debug_url = event_data.get("debug_url", "")
                                        if debug_url:
                                            logger.info(f"工作流执行完成，调试URL: {debug_url}")
                                            # 如果已经有解析结果，现在可以返回了
                                            if parsed_result:
                                                return parsed_result
                                    elif event_type == "Error":
                                        error_data = event_data
                                        logger.error(f"收到错误事件: {error_data}")
                                        # 在错误事件中也记录debug_url
                                        if debug_url:
                                            logger.info(f"工作流调试URL: {debug_url}")
                                        raise Exception(f"工作流执行错误: {error_data}")
                                    
                                except json.JSONDecodeError as e:
                                    logger.error(f"解析事件数据失败: {str(e)}, 数据: {data_str[:100]}...")
                            else:
                                # 空数据行，可能是事件结束
                                current_event_type = None
                    
                    # 流结束后，如果有解析结果但没有收到Done事件
                    if parsed_result:
                        if debug_url:
                            logger.info(f"工作流调试URL: {debug_url}")
                        return parsed_result
                    
            logger.error(f"未找到有效的解析结果，共收到{len(all_data)}个事件")
            if all_data:
                logger.error(f"最后一个事件: {all_data[-1]}")
            # 确保在解析失败时也输出debug_url
            if debug_url:
                logger.info(f"工作流调试URL: {debug_url}")
            raise Exception("未找到有效的解析结果")
            
        except Exception as e:
            # 确保在异常情况下也输出debug_url
            if 'debug_url' in locals() and debug_url:
                logger.info(f"工作流调试URL: {debug_url}")
            logger.error(f"PDF解析失败: {str(e)}")
            raise
    
    def _count_table_cells(self, line: str) -> int:
        """
        计算表格行的单元格数量，按照标准化方法
        
        Args:
            line: 表格行文本
        
        Returns:
            int: 单元格数量
        """
        # 步骤1: 去除首尾空白
        trimmed_line = line.strip()
        
        # 步骤2: 按竖线|分割
        split_parts = trimmed_line.split("|")
        
        # 步骤3: 过滤空字符串
        filtered_parts = [part for part in split_parts if part.strip() != ""]
        
        # 步骤4: 统计有效长度
        return len(filtered_parts)
            
    def _process_workflow_result(self, parsed_content: Dict[str, Any]) -> PDFParseResult:
        """
        处理工作流返回的结果，生成最终的Markdown
        
        Args:
            parsed_content: 工作流返回的解析内容
        
        Returns:
            PDFParseResult: 处理后的结果
        """
        try:
            parsed_array = parsed_content.get("parsed_array", [])
            possible_titles = parsed_content.get("possible_titles", [])
            
            # Step 1: 解析parsed_array为列表A
            list_a = []
            for item in parsed_array:
                list_a.append({
                    "id": item.get("id"),
                    "label": item.get("label", "text"),
                    "content": item.get("content", "")
                })
            
            # Step 2: 解析possible_titles为列表B
            list_b = []
            for title in possible_titles:
                list_b.append({
                    "id": int(title.get("id")),
                    "label": "title",
                    "content": title.get("content", "")
                })
            
            # Step 3: 根据possible_titles修正parsed_array中的标题
            final_list = self._merge_title_corrections(list_a, list_b)
            
            # Step 4: 按id排序并生成markdown，优化表格处理
            final_list.sort(key=lambda x: x.get("id", 0))
            
            # 优化表格处理的新逻辑
            markdown_lines = []
            i = 0
            while i < len(final_list):
                current = final_list[i]
                current_content = current["content"]
                markdown_lines.append(current_content)
                
                # 检查是否可能是表格行开始
                is_table_start = False
                if current_content.count("|") >= 2 and i + 1 < len(final_list):
                    next_content = final_list[i + 1]["content"]
                    
                    # 步骤1: 检查下一行是否只包含 |、-、:、空格 四种字符
                    if re.match(r'^[|\-: ]*$', next_content):
                        # 步骤2: 计算表头行和候选分隔线行的单元格数量
                        header_cell_count = self._count_table_cells(current_content)
                        divider_cell_count = self._count_table_cells(next_content)
                        
                        # 步骤3: 检查单元格数量是否一致
                        if header_cell_count == divider_cell_count and header_cell_count > 0:
                            # 步骤4: 检查每个单元格是否至少有一个 -
                            split_divider = next_content.split("|")
                            valid_divider = True
                            for part in split_divider:
                                if part.strip() and "-" not in part:
                                    valid_divider = False
                                    break
                            
                            if valid_divider:
                                is_table_start = True
                                # 以下打印只是调试用，测试的时候可以启用
                                # print(f"表格开始行: {current_content}")
                                # print(f"分隔线行: {next_content}")
                
                if is_table_start:
                    # 处理表格
                    table_pipe_count = final_list[i + 1]["content"].count("|")
                    markdown_lines.append(final_list[i + 1]["content"])
                    i += 2  # 跳过当前行和分隔线行
                    
                    # 收集表格内容行
                    table_ended = False
                    while i < len(final_list):
                        content_line = final_list[i]["content"]
                        if content_line.count("|") == table_pipe_count:
                            markdown_lines.append(content_line)
                            i += 1
                        else:
                            # 表格结束，先添加空行，再添加当前行内容
                            markdown_lines.append("")  # 添加一个空行
                            markdown_lines.append(content_line)
                            table_ended = True
                            i += 1
                            break
                     
                    # 如果表格是最后一个元素，也要添加空行
                    if not table_ended:
                        markdown_lines.append("")
                else:
                    i += 1
            
            markdown_content = "\n".join(markdown_lines)
            
            return PDFParseResult(
                markdown_content=markdown_content,
                parsed_array=parsed_array,
                possible_titles=possible_titles
            )
            
        except Exception as e:
            logger.error(f"处理工作流结果失败: {str(e)}")
            raise
    
    def _merge_title_corrections(self, list_a: List[Dict], list_b: List[Dict]) -> List[Dict]:
        """
        合并标题修正信息
        
        Args:
            list_a: 原始解析结果
            list_b: 标题修正信息
            
        Returns:
            合并后的列表
        """
        # 创建id到修正标题的映射
        title_corrections = {item["id"]: item for item in list_b}
        
        # 应用修正
        result = []
        for item in list_a:
            if item["id"] in title_corrections:
                # 使用修正后的标题
                result.append(title_corrections[item["id"]])
            else:
                # 保持原样
                result.append(item)
                
        return result
    
    def _download_images(self, markdown_content: str, output_dir: str) -> str:
        """
        下载Markdown内容中的网络图片并保存到本地
        
        Args:
            markdown_content: Markdown内容
            output_dir: 输出目录
        
        Returns:
            str: 修改后的Markdown内容
        """
        # 创建图片目录
        images_dir = os.path.join(output_dir, 'images')
        os.makedirs(images_dir, exist_ok=True)
        
        # 查找所有图片链接
        image_pattern = r'!\[.*?\]\((.*?)\)'        
        def replace_image_url(match):
            image_url = match.group(1)
            if image_url.startswith('http'):
                # 下载图片
                try:
                    import requests
                    response = requests.get(image_url, timeout=10)
                    response.raise_for_status()
                    
                    # 生成唯一文件名
                    file_ext = image_url.split('.')[-1].split('?')[0].lower()
                    if file_ext not in ['jpg', 'jpeg', 'png', 'gif', 'bmp']:
                        file_ext = 'jpeg'
                    
                    image_name = f'{uuid.uuid4()}.{file_ext}'
                    image_path = os.path.join(images_dir, image_name)
                    
                    with open(image_path, 'wb') as f:
                        f.write(response.content)
                    
                    # 返回本地图片路径
                    return f'![image](images/{image_name})'
                except Exception as e:
                    logger.error(f'下载图片失败: {str(e)}')
                    # 下载失败时保留原链接
                    return match.group(0)
            else:
                # 已经是本地路径，无需修改
                return match.group(0)
        
        # 替换所有图片链接
        modified_content = re.sub(image_pattern, replace_image_url, markdown_content)
        return modified_content
    
    async def parse_pdf_file(self, file_path: str, output_dir: Optional[str] = None) -> PDFParseResult:
        """
        完整的PDF解析流程
        
        Args:
            file_path: PDF文件路径
            output_dir: 输出目录，可选
        
        Returns:
            PDFParseResult: 解析结果
        """
        logger.info(f'开始解析PDF文件: {file_path}')
        
        # 确定输出目录
        pdf_filename = Path(file_path).stem
        if not output_dir:
            # 默认输出目录
            default_output_dir = Path("../tmp/results")
            output_dir = default_output_dir / pdf_filename
            output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f'使用默认输出目录: {output_dir}')
        else:
            # 使用指定的输出目录
            output_dir_path = Path(output_dir)
            if not output_dir_path.exists():
                output_dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f'创建指定输出目录: {output_dir_path}')
        
        # Step 1: 上传文件
        file_id = await self.upload_file(file_path)
        
        # Step 2: 调用工作流解析
        result = await self.parse_pdf_stream(file_id)
        
        # Step 3: 下载图片并修改Markdown内容
        modified_markdown = self._download_images(result.markdown_content, str(output_dir))
        result = PDFParseResult(
            markdown_content=modified_markdown,
            parsed_array=result.parsed_array,
            possible_titles=result.possible_titles
        )
        
        # 保存解析结果到文件
        output_file = Path(output_dir) / f"{pdf_filename}.md"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(result.markdown_content)
        logger.info(f'解析结果已保存到: {output_file}')
        
        logger.info(f'PDF解析完成，生成内容长度: {len(result.markdown_content)}')
        return result