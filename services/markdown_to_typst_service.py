"""
Markdown转Typst服务
使用cmarker库将Markdown文件转换为Typst格式
"""

import os
import tempfile
from typing import Optional
from pathlib import Path
from core.logging import get_logger
from config import settings

# 导入extract_cmarker_source库
import cmarker_python

glogger = get_logger(__name__)


class MarkdownToTypstService:
    """Markdown转Typst服务"""

    def __init__(self):
        # 目前不需要特别的配置
        pass

    def convert_markdown_to_typst(self, markdown_content: str) -> str:
        """
        将Markdown内容转换为Typst格式

        Args:
            markdown_content: Markdown内容

        Returns:
            str: 转换后的Typst内容
        """
        try:
            glogger.info(f"开始转换Markdown到Typst，内容长度: {len(markdown_content)}")

            # 调用cmarker库进行转换
            # 参考lib.rs中的API定义
            # 使用convert_md_to_typ函数，需要提供输入和输出文件路径
            # 由于我们已经读取了内容，这里我们创建临时文件来处理
            with tempfile.NamedTemporaryFile(suffix=".md", delete=False, mode="w", encoding="utf-8") as tmp_input:
                tmp_input.write(markdown_content)
                tmp_input_path = tmp_input.name

            with tempfile.NamedTemporaryFile(suffix=".typst", delete=False, mode="w", encoding="utf-8") as tmp_output:
                tmp_output_path = tmp_output.name

            try:
                # 调用cmarker_python的convert_md_to_typ函数
                cmarker_python.convert_md_to_typ(tmp_input_path, tmp_output_path)

                # 读取转换后的内容
                with open(tmp_output_path, 'r', encoding='utf-8') as f:
                    typst_content = f.read()
            finally:
                # 清理临时文件
                os.remove(tmp_input_path)
                os.remove(tmp_output_path)

            glogger.info(f"Markdown转换完成，生成Typst内容长度: {len(typst_content)}")
            return typst_content
        except Exception as e:
            glogger.error(f"Markdown转换Typst失败: {str(e)}")
            raise

    def convert_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """
        转换Markdown文件为Typst文件

        Args:
            file_path: Markdown文件路径
            output_path: 输出Typst文件路径，可选

        Returns:
            str: 输出文件路径
        """
        try:
            glogger.info(f"开始转换文件: {file_path}")

            # 读取Markdown文件
            with open(file_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 转换内容
            typst_content = self.convert_markdown_to_typst(markdown_content)

            # 确定输出路径
            if not output_path:
                # 默认输出路径
                md_filename = Path(file_path).stem
                output_path = f"{md_filename}.typ"

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 写入Typst文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(typst_content)

            glogger.info(f"转换结果已保存到: {output_path}")
            return output_path
        except Exception as e:
            glogger.error(f"文件转换失败: {str(e)}")
            raise

    async def convert_file_async(self, file_path: str, output_path: Optional[str] = None) -> str:
        """
        异步版本的文件转换

        Args:
            file_path: Markdown文件路径
            output_path: 输出Typst文件路径，可选

        Returns:
            str: 输出文件路径
        """
        # 注意：这里实际上是同步操作，但为了保持API一致性，提供异步接口
        return self.convert_file(file_path, output_path)