# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
Always respond in English.

1) Purpose & Role

This document instructs <PERSON> how to work in this repository: what the system does, where key code lives, how to run/test it, security expectations, and the reply format for proposed changes.

2) Project Snapshot

AI Report Generator — converts Word docs into web content and provides real-time, ChatGPT-style AI writing via SSE, with Typst PDF generation.

Backend: Python 3.13, FastAPI, SQLAlchemy, Alembic, MySQL (PyMySQL)

AI/LLM: OpenAI API, Volcano Engine API, streaming responses

Docs/Media: mammoth, python-docx, Pillow, Typst engine (Rust+Python)

Frontend: Next.js 15, React 18, TypeScript, TipTap/Novel, Typst Viewer

Streaming: Server-Sent Events (SSE)

Charts: D3.js config generation

Typst: Rust-based markdown-to-typst conversion, PDF generation

Pkg Mgmt: uv (preferred), pip (fallback)

3) Runbook (dev)
# Backend
uv install
uv run python main.py          # dev server
# Alt:
# pip install -r requirements.txt
# python main.py

# Database
alembic upgrade head
# To create a migration:
alembic revision --autogenerate -m "desc"

# Frontend
cd web/
pnpm install
pnpm run dev                   # http://localhost:3000
pnpm run build
pnpm run lint
pnpm run type-check

4) Repository Map (essentials)

main.py — FastAPI app, CORS/middleware/errors

api/

auth_routes.py — JWT login/refresh, RBAC guards

report_routes.py — report mgmt & AI chat endpoints

word_format_routes.py — Word upload + processing

pdf_routes.py — PDF upload & Coze workflow processing

markdown_to_typst_routes.py — Markdown to Typst conversion endpoints

ai_stream_routes.py — SSE streaming endpoints

services/

word_parser.py — Word → Markdown (tables → placeholders)

pdf_parser.py — PDF → Markdown via Coze workflow API

chart_xml_parser.py — Chart XML metadata extraction

word_image_extractor.py — Images & chart data from Word

d3js_generator.py — XML → D3.js configs

chart_service.py — Orchestrates doc → chart pipeline

markdown_to_typst_service.py — Markdown to Typst conversion service

ai_chat_service.py — LLM calls, optimization & text ops

auth_service.py — JWT/session mgmt

core/

security.py — JWT token validation and RBAC decorators

rate_limit.py — Memory sliding window rate limiting

security_headers.py — OWASP security headers middleware

request_context.py — Request context and disconnect detection

database/ — connections & in-memory store

models/ — SQLAlchemy models & Pydantic schemas

alembic/ — DB migrations

web/src/

components/ai-editor/ — TipTap/Novel editor & AI UI

components/chart-renderer.tsx — Renders D3 charts from backend configs

components/markdown-renderer.tsx — Markdown with [CHART:chart_id]

components/TypstCoreViewer.tsx — Typst document preview component

lib/ai-api.ts hooks/use-ai.ts — SSE client & state mgmt

app/api/ — Next.js API routes (unified service layer)

app/api/prose/generate/route.ts — Dev proxy (CORS-free)

app/typst-demo/ — Typst preview demo page

lib/api-client.ts — Injects Bearer tokens

5) Architecture (at a glance)
Word (.doc/.docx) / PDF
  → mammoth/python-docx extract text/tables/images
  → Coze workflow API for PDF → Markdown conversion
  → chart_xml_parser + word_image_extractor detect chart metadata
  → d3js_generator builds D3-compatible JSON
  → FastAPI serves Markdown + chart configs
  → Next.js API routes (unified service layer) proxy requests
  → Frontend renders Markdown + D3
  → AI streaming (SSE) provides real-time writing assistance
  → Rust-based Typst engine converts Markdown to professional PDF

Security: JWT authentication, RBAC, rate limiting, secure headers, file upload validation.

Chart placeholders in Markdown: [CHART:<chart_id>] (frontend resolves via backend config fetch).

Typst Integration: Rust core engine with Python bindings for high-performance document compilation.

6) Security Expectations (enforced)

Auth: JWT Bearer + refresh; sessions rotate; bcrypt passwords; RBAC roles.

Rate limits (sliding window, configurable): login ~10/60s, refresh ~30/60s, AI gen ~60/60s.

Headers: OWASP-aligned (e.g., X-Content-Type-Options, Referrer-Policy) + X-Request-ID tracing.

Uploads: Path traversal blocked; only .doc/.docx; magic-number validation.

SSE: Requires valid Bearer token; handle disconnect cleanup.

When proposing changes that touch auth, uploads, or SSE, call out security implications and test steps explicitly.

7) How Claude Should Respond (format contract)

When I ask for a change, respond with:

Plan – 3–7 concise steps (what & why).

Diffs – unified diffs per file (small, focused hunks).

Commands – exact shell commands to run (backend/frontend/migrations).

Verification – steps & expected results (incl. SSE and auth flows).

Fallbacks – rollback notes (e.g., alembic downgrade -1).

Keep replies terse; no screenshots; prefer diffs over prose.

8) Coding Conventions & Guardrails

Prefer uv run python …; keep Python 3.13 compatibility.

FastAPI style: dependency-injected services; typed Pydantic models.

SQLAlchemy: explicit models; avoid N+1; migrations via Alembic only.

Streaming: never block the event loop; yield tokens promptly; robust client-disconnect handling.

Frontend: functional React with TypeScript; small, testable components; TipTap extensions isolated; avoid client-only secrets.

Charts: D3 configs must be pure JSON; keep rendering responsive; avoid heavy computation client-side.

Do not hardcode secrets, change CORS broadly, weaken rate limits, or bypass RBAC.

9) Test Guidance (what “done” looks like)

Backend smoke: start server, hit /health (if present), login → refresh → authorized SSE connect.

Upload path: bad extension rejected; magic-number mismatch rejected; .docx with a chart yields [CHART:…] and retrievable D3 config.

SSE path: first token < ~500ms locally; stream ends cleanly; disconnect handled.

RBAC: non-admin blocked from admin endpoints; tokens rotate on refresh.

Frontend: editor streams tokens in real time; chart placeholders render; no CORS errors.
