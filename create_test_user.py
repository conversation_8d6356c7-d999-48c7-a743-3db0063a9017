#!/usr/bin/env python3
"""
创建测试用户脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database.connection import get_db
from services.auth_service import AuthService
from models.schemas import UserCreate, UserR<PERSON>

def create_test_user():
    """创建测试用户"""
    # 获取数据库会话
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        # 检查是否已存在测试用户
        existing_user = AuthService.get_user_by_email(db, "<EMAIL>")
        if existing_user:
            print("测试用户已存在:")
            print(f"  ID: {existing_user.id}")
            print(f"  用户名: {existing_user.username}")
            print(f"  邮箱: {existing_user.email}")
            print(f"  角色: {existing_user.role}")
            return existing_user
        
        # 创建测试用户
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            password="testpass123",
            role=UserRole.USER
        )
        
        user = AuthService.create_user(db, user_data)
        print("测试用户创建成功:")
        print(f"  ID: {user.id}")
        print(f"  用户名: {user.username}")
        print(f"  邮箱: {user.email}")
        print(f"  角色: {user.role}")
        
        # 创建管理员用户
        admin_existing = AuthService.get_user_by_email(db, "<EMAIL>")
        if not admin_existing:
            admin_data = UserCreate(
                username="admin",
                email="<EMAIL>",
                full_name="Admin User",
                password="admin123",
                role=UserRole.ADMIN
            )
            admin_user = AuthService.create_user(db, admin_data)
            print("\n管理员用户创建成功:")
            print(f"  ID: {admin_user.id}")
            print(f"  用户名: {admin_user.username}")
            print(f"  邮箱: {admin_user.email}")
            print(f"  角色: {admin_user.role}")
        else:
            print("\n管理员用户已存在:")
            print(f"  ID: {admin_existing.id}")
            print(f"  用户名: {admin_existing.username}")
            print(f"  邮箱: {admin_existing.email}")
            print(f"  角色: {admin_existing.role}")
            
        return user
        
    except Exception as e:
        print(f"创建用户失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()