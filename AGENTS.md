# 通用 Agent 协作规范（公共可复用版，ai-report-gen 适配）

## 目标与范围
- 明确目标: 统一本仓库内 Agent/贡献者的协作方式，提升可维护性与可信度。
- 适用对象: 参与代码实现、测试、实验与文档维护的自动化代理与人类贡献者。

## 项目结构（仓库适配）
- 源码: `api/`（路由）、`services/`（业务）、`models/`、`core/`、`database/`（含 `alembic/`）。
- 前端: `web/`（Next.js）。
- Typst工具链: `rust-tools/`（Rust核心引擎+Python绑定）、`wheels/`（预构建轮子）、`assets/template/`（模板）。
- 工具与测试: `scripts/` 存放集成/验证脚本（`test_*.py`）。建议新增单元测试放在 `tests/`。
- 配置/资源: `.env`/`.env.example`、`assets/`、`fonts/`、`templates`（如有）。
- 运行产物: `logs/`、`output/`、`temp/`、`tmp/`（大产物请 gitignore）。

## 构建/测试/运行
- 构建/依赖: `uv install`（Python 3.13+），前端 `cd web && pnpm install`。
- Typst工具链: 使用预构建轮子或 `cd rust-tools/extract-tool && maturin build --release`。
- 数据库: `alembic upgrade head`（禁止绕过迁移直改 DDL）。
- 运行后端: `uv run python main.py`（API 文档 `/docs`）。
- 运行前端: `cd web && pnpm dev`。
- 测试（现状）: 运行 `scripts/test_*.py`，示例：`uv run python scripts/test_summary_report.py`。
- Typst测试: `uv run python scripts/test_markdown_to_typst.py`。
- 测试（推荐）: `pytest -v --cov=api --cov=services --cov-report=term-missing`（将用 `tests/` 目录）。

## 代码风格与命名
- 语言/版本: Python 3.13，Node 24（前端）。
- 风格: `black`（88 列）+ `isort`（profile=black），`flake8`，`mypy` 严格模式。
- 命名: 模块/函数 `snake_case`，类 `PascalCase`，常量 `UPPER_SNAKE_CASE`；路由文件 `<feature>_routes.py`。
- 设计: KISS/YAGNI/DRY，优先复用 `services/` 与现有抽象；避免跨层耦合与重复代码。
- 副作用: 文档解析/图表生成/AI 推理/DB 操作等放入可控边界（服务层），对外纯函数优先。

## 工作流程（理解→规划→执行→汇报）
- 理解: 变更前阅读相关模块与配置（`config.py`、路由、服务、迁移）。
- 规划: 简述影响面、验证路径与回退方案；必要时给替代方案或特性开关。
- 执行: 小步提交，遵守目录/命名/接口；变更 DB 必须配套 Alembic 迁移。
- 汇报: 用要点说明目的/影响、运行与测试方式、注意事项与风险点。

## 变更粒度与提交
- 小步提交: 聚焦单一主题，避免夹带不相关重构；建议 ≤ 200 行/次。
- 提交信息: Conventional Commits（`feat:`, `fix:`, `docs:`, `refactor:` 等，可加 scope）。
- PR 要求: 清晰摘要、关联 issue（如 `Fixes #123`）、what/why、运行说明、关键截图/API 示例；变更范围干净。

## 测试门禁
- 必测范围: `api/` 路由、`services/` 核心逻辑、认证与安全中间件（`core/`）、DB 迁移/模型（`database/`/`models/`）。
- 决定性: 能 mock 的尽量 mock；AI/外部依赖用假实现或固定输入输出；必要时固定随机种子。
- 覆盖点: 解析/校验可行性、主要分支与错误处理、SSE/流式边界、Auth 校验、数据落库与回滚。
- 位置与命名: 单测放 `tests/`，文件 `test_*.py`；集成/回归脚本保留在 `scripts/`。

## 配置与数据安全
- 位置: 仅在 `.env`/`config.py` 或 `assets/`/`alembic/` 调整配置与数据；禁止把密钥写入源码。
- 数据合规: 仅提交小型、匿名化数据；不要提交隐私或敏感内容。
- 产物管理: 大体量产物与中间文件放 `output/`、`logs/`、`tmp/`，并 gitignore。
- 编码: 统一 UTF‑8；注意不同平台换行与控制台编码。

## 高风险模块变更流程
- 范围: 认证/授权（`api/auth_routes.py`、`core/*`）、SSE/流式（`api/ai_stream_routes.py`）、PDF/Word 解析、图表渲染、Typst转换（`services/markdown_to_typst_service.py`、`rust-tools/`）、DB 结构与迁移、OpenAI/LLM 接入。
- 要求: 先确认问题与假设；不确定多沟通；必要时提供特性开关或灰度配置。
- 校验: 明确可行性与性能/指标影响，提供对照实验或日志/结果片段。
- 回滚: 变更尽量可开关/可回退，不做一次性不可逆替换。

## 性能与策略
- 原则: 先正确性再性能；先固化可运行基线，再迭代优化。
- 偏离说明: 与既有参数/策略不同的选择需要给出原因、收益与潜在副作用。
- 评估: 必要时提供基准命令与图表/日志（可放 `results/` 或 `output/`，勿提交大文件）。

## 调试与可视化
- 调试开关: 使用 `settings.debug`、`log_level` 与 `core/logging.py` 的日志能力，默认关闭高噪声日志。
- 可视化: 视需要提供脚本输出 D3/图表或中间结果，便于定位与沟通。
- 结果存档: 批量运行建议放 `results/batch_runs/`（或 `output/` 子目录），附生成命令与读我。

## 文档与知识沉淀
- 同步更新: 重要策略、参数或接口变更需更新 `README.md`、`AGENTS.md`、相关指南（如 `AUTHENTICATION_GUIDE.md`）。
- 设计记录: 关键决策、权衡与实验结果简要记录，便于复盘与扩展。

## 沟通约定
- 透明沟通: 重要修改前同步计划，实施中定期进度更新，结束后汇报。
- 语言与风格: 以中文为主（英文亦可），输出清晰、简洁、可操作。
- 约束意识: 遵守仓库路径/命名/工具链（uv/pytest/alembic/pnpm）；遇未定义情况先询问再落地。

## 适配事项（可选）
- 脚本/命令: 如需，可新增 Makefile/npm scripts 或 `scripts/` 一键脚本（与现有命令一致）。
- CI/CD: 如接入 CI，确保新增/变更不破坏默认流水；必要时补充步骤说明。
- 多平台: 注意路径分隔符与大小写差异；避免平台相关硬编码。

## Conventional Commits（示例）
- `feat(scope): 新增功能`
- `fix(scope): 修复缺陷`
- `refactor(scope): 重构（非功能/性能）`
- `perf(scope): 性能改进`
- `test(scope): 测试相关`
- `docs(scope): 文档更新`
- `build(scope)/ci(scope)/chore(scope)`: 构建、CI 或杂项
- `revert: 回滚提交`

## Prioritize using MCP service

- `Context7`: Query latest library documentation/examples
- `Exa`: Use Exa AI for web search - real-time web search, can capture specific URL content. Supports configurable result count and returns most relevant website content
