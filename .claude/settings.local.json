{"permissions": {"allow": ["<PERSON><PERSON>(curl:*)", "Bash(claude-code config:*)", "Bash(pnpm build)", "mcp__memory__read_graph", "mcp__serena__list_memories", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__write_memory", "mcp__exa__web_search_exa", "mcp__context7__resolve-library-id", "mcp__exa__deep_researcher_start", "mcp__exa__deep_researcher_check", "Bash(pnpm add:*)", "Bash(pnpm run type-check:*)", "Bash(pnpm run dev:*)", "mcp__serena__find_symbol", "mcp__serena__replace_regex", "mcp__serena__replace_symbol_body", "mcp__serena__search_for_pattern", "mcp__serena__insert_after_symbol", "Bash(git add:*)", "Bash(git commit:*)", "mcp__serena__find_file", "mcp__context7__get-library-docs", "Bash(grep:*)", "Bash(git checkout:*)", "Bash(gh issue:*)", "mcp__exa__crawling_exa", "Bash(git push:*)", "Bash(rg:*)", "<PERSON><PERSON>(alembic current:*)", "<PERSON><PERSON>(alembic history:*)", "Bash(pnpm run build)", "Bash(pnpm run lint)", "Bash(gh pr view:*)", "<PERSON><PERSON>(gh pr checks 9)", "Bash(gh pr list:*)", "WebFetch(domain:myriad-dreamin.github.io)", "WebSearch"], "deny": []}}