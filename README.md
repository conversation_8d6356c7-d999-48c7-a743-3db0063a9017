# 🚀 AI智能报告生成系统

> **基于AI的文档处理与实时流式编辑系统** - 集成Word文档转换、图表智能优化和AI写作助手的完整解决方案

## 📋 项目概述

本系统是一个现代化的AI驱动文档处理平台，实现从Word文档到Web可用内容的完整转换流程，并提供实时流式AI写作助手功能。通过先进的AI技术，系统能够自动提取、优化文档内容，生成专业级数据可视化图表，并提供类似ChatGPT的实时AI写作体验。

### 🎯 核心功能

#### 📄 智能文档处理
- **Word文档解析**: 完整提取文档结构、表格和图表
- **PDF文档解析**: 基于Coze工作流的智能PDF转Markdown
- **图表智能提取**: 自动识别Excel图表并提取XML数据
- **AI驱动优化**: 基于火山引擎API的智能图表优化
- **D3.js生成**: 生成Web可用的专业图表配置
- **Markdown集成**: 保持原始布局的文档转换

#### ✨ 实时AI写作助手
- **🚀 实时流式响应**: 类似ChatGPT的实时文本生成体验
- **🎯 智能文本优化**: 改进、缩短、扩展、修正语法等功能
- **💡 上下文感知**: 基于选中文本的智能建议
- **🎨 现代化界面**: 响应式设计，支持多设备访问
- **⚡ 高性能流式**: Server-Sent Events实现毫秒级响应

## 🏗️ 系统架构

### 完整处理流程
```
Word/PDF文档上传 → 内容解析 → AI优化 → Web展示 → 实时编辑 → Typst编译
     ↓             ↓         ↓        ↓         ↓          ↓
   mammoth       图表提取  火山引擎API  D3.js渲染  流式AI助手  Typst生成
   docx/PDF      XML解析   智能优化   响应式布局   实时更新    PDF输出
   Coze工作流    Rust引擎   上下文理解  模板渲染   Markdown    专业排版
```

### 技术架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend       │    │   AI Services   │
│                 │    │                  │    │                 │
│ • Next.js 15    │◄──►│ • FastAPI        │◄──►│ • 火山引擎API    │
│ • React 18      │    │ • SQLAlchemy     │    │ • OpenAI兼容    │
│ • TipTap Editor │    │ • Alembic        │    │ • 流式响应      │
│ • SSE Client    │    │ • SSE Streaming  │    │ • 上下文理解    │
│ • D3.js Charts  │    │ • CORS Support   │    │ • 智能优化      │
│ • Typst Viewer  │    │ • Rust Tools     │    │ • PDF解析       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              ↕
                    ┌─────────────────┐
                    │  Typst Engine   │
                    │                 │
                    │ • Rust核心      │
                    │ • Python绑定   │
                    │ • 模板系统      │
                    │ • 字体支持      │
                    │ • PDF生成       │
                    └─────────────────┘
```

### 🆕 最新更新 (2025-08-26)

#### 📝 Typst文档编译集成 - PR #14 ✅
- ✅ **Rust核心引擎**: 基于Rust的高性能Markdown到Typst转换引擎
- ✅ **Python绑定**: 使用PyO3和Maturin的无缝Python集成
- ✅ **API端点支持**: 新增 `/api/markdown-to-typst/` 路由组，支持文档转换
- ✅ **实时预览组件**: Web端Typst实时预览界面 (`/typst-demo`)
- ✅ **跨平台轮子**: Linux和Windows x86_64预构建轮子支持
- ✅ **用户隔离**: 基于用户的文档管理和权限控制
- ✅ **字体支持**: 完整的中文字体配置 (Noto Sans/Serif CJK, Ubuntu等)
- ✅ **模板系统**: 可自定义的Typst模板 (`assets/template/`)

#### 🔧 数据库迁移修复 ✅
- ✅ **迁移链修复**: 解决Alembic分支冲突，恢复线性历史
- ✅ **模式同步**: 数据库模型定义与实际状态完全同步
- ✅ **JSON字段优化**: processed_documents表JSON字段可空性修复
- ✅ **迁移验证**: 完整的迁移链验证通过

#### 🏗️ 项目结构优化 ✅
- ✅ **Rust工具目录**: 独立的 `rust-tools/` 目录结构
- ✅ **文档规范**: 新增通用Agent协作规范 (`AGENTS.md`)
- ✅ **依赖管理**: 更新 `.gitignore` 和依赖配置
- ✅ **测试脚本**: Typst转换流程测试工具
- ✅ **日志优化**: 减少SQLAlchemy冗余输出

### 🆕 先前更新 (2025-08-15)

#### 📄 PDF智能解析支持 ✅
- ✅ **Coze工作流集成**: 基于Coze API的PDF智能解析
- ✅ **PDF上传接口**: 新增 `/upload-pdf` 端点支持PDF文件处理
- ✅ **智能格式转换**: PDF自动转换为结构化Markdown
- ✅ **文件管理**: PDF文件和解析结果统一存储在 `tmp/` 目录
- ✅ **测试工具**: 完整的PDF解析流程测试脚本
- ✅ **环境配置**: Coze API密钥和工作流ID配置支持

#### 🛠️ 数据库结构优化 ✅
- ✅ **字段约束加强**: processed_documents表中tables和charts字段设为非空
- ✅ **默认值处理**: 空列表作为JSON字段默认值，确保数据一致性
- ✅ **迁移脚本**: 完整的Alembic数据库迁移支持
- ✅ **错误防护**: 防止AI服务不可用时的空引用错误

#### 🔒 安全加固实施 - 全面验证通过 ✅
- ✅ **JWT认证系统**: 完整的Bearer令牌认证，支持刷新令牌旋转
- ✅ **基于角色的访问控制**: 用户角色管理，bcrypt密码加密
- ✅ **速率限制保护**: 内存滑动窗口算法 (登录10/60s, 刷新30/60s, AI生成60/60s)
- ✅ **安全头部设置**: OWASP标准安全头，请求追踪标识
- ✅ **文件上传安全**: 路径遍历防护，魔数签名验证
- ✅ **SSE流式认证**: 服务器推送事件的完整身份验证
- ✅ **前端集成**: 自动令牌注入，CORS配置，代理转发

#### 🌐 Next.js服务层架构重构 ✅
- ✅ **统一服务层**: Next.js API路由作为中间件处理前后端通信
- ✅ **TypeScript类型安全**: 增强的Chart、Table和UI组件类型定义
- ✅ **ESLint规则优化**: 改进的类型安全和认证工具使用规则
- ✅ **API客户端重构**: 使用Next.js服务层的统一API客户端
- ✅ **认证流程优化**: 改进的认证处理和错误管理
- ✅ **代理路由**: 移除直接后端端口暴露，增强安全性
### 🆕 先前更新 (2025-08-07)
- ✅ **代码重构**: 简化了服务，移除了数据查询和Text2SQL功能，专注于核心文档处理和AI写作。
- ✅ **实时流式AI响应**: 完整的Server-Sent Events实现
- ✅ **AI写作助手界面**: 现代化的模态界面，支持长内容滚动
- ✅ **多种AI功能**: 文本改进、缩短、扩展、语法修正等
- ✅ **响应式设计**: 完善的移动端和桌面端适配
- ✅ **性能优化**: 毫秒级响应和流畅的用户体验

## 🛠️ 技术栈

### 后端核心
- **Python 3.11+**: 核心开发语言
- **FastAPI**: 现代化Web框架，支持异步和SSE
- **SQLAlchemy + Alembic**: 数据库ORM和迁移
- **mammoth + python-docx**: Word文档处理
- **OpenAI API**: AI服务集成 (火山引擎)

### 前端技术
- **Next.js 15**: 最新版本，App Router架构
- **React 18**: 用户界面构建
- **TypeScript**: 类型安全开发
- **TipTap + Novel**: 富文本编辑器
- **D3.js**: 数据可视化
- **Tailwind CSS**: 现代化样式框架
- **Framer Motion**: 流畅动画效果


## 🚀 快速开始

### 1. 环境要求

**系统要求**:
- Python 3.11+ 
- Node.js 18+
- MySQL 8.0+

**推荐工具**:
- uv (Python包管理)
- pnpm (Node.js包管理)

### 2. 后端设置

```bash
# 克隆项目
git clone <repository-url>
cd ai-report-gen

# 安装Python依赖 (推荐使用uv)
uv install

# 或使用传统方式
pip install -r requirements.txt
# 安装cmarker (确保项目携带extract_cmarker_source-0.1.0-cp37-abi3-win_amd64.whl这个文件)
pip install extract_cmarker_source-0.1.0-cp37-abi3-win_amd64.whl
# 配置环境变量
cp .env.example .env
```

#### 环境变量配置 (.env)
```env
# 应用配置
APP_NAME="AI REPORT API"
DEBUG=false
HOST="0.0.0.0"
PORT=8000

# 数据库配置
DB_HOST="localhost"
DB_PORT=3306
DB_USER="root"
DB_PASSWORD="your_password"
DB_DATABASE="travel_data"

# LLM配置 (关键配置)
LLM_API_KEY="your_volcano_engine_api_key"
LLM_BASE_URL="https://ark.cn-beijing.volces.com/api/v3"
LLM_MODEL_NAME="ep-20250701150532-w476k"
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2000

# 认证配置 (新增)
AUTH_SECRET_KEY="your-super-secret-jwt-key-change-in-production"
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=30
AUTH_REFRESH_TOKEN_EXPIRE_HOURS=168  # 7 days
```

```bash
# 运行数据库迁移
alembic upgrade head

# 创建测试用户 (可选，用于测试认证)
uv run python create_test_user.py

# 启动后端服务
uv run python main.py
```

### 3. 前端设置

```bash
# 进入前端目录
cd web/

# 安装依赖
pnpm install

# 配置环境变量
echo "NEXT_PUBLIC_AI_BACKEND_URL=http://localhost:8000" > .env.local

# 启动开发服务器
pnpm run dev
```

### 4. 访问应用

- **🏠 主应用**: http://localhost:3000
- **🧪 流式测试**: http://localhost:3000/test-streaming
- **📚 API文档**: http://localhost:8000/docs
- **❤️ 健康检查**: http://localhost:8000/ping

## 🧪 测试和验证

### 后端测试
```bash
# 测试LLM连接
uv run python scripts/test_llm_config.py

# 测试完整D3.js流程
uv run python scripts/test_complete_d3_pipeline.py

# 测试AI优化功能
uv run python scripts/test_ai_optimization.py

# 测试前端集成
uv run python scripts/test_frontend_integration.py

# 生成测试报告
uv run python scripts/test_summary_report.py
```

### 前端测试
```bash
cd web/

# TypeScript类型检查
pnpm run type-check

# ESLint代码检查
pnpm run lint

# 构建测试
pnpm run build
```

## 🔧 开发指南

### 项目结构
```
ai-report-gen/
├── api/                    # FastAPI路由模块
│   ├── ai_stream_routes.py # AI流式接口
│   ├── word_format_routes.py # 文档处理接口
│   ├── markdown_to_typst_routes.py # Markdown转Typst接口
│   └── ...
├── services/               # 核心业务逻辑
│   ├── ai_chat_service.py  # AI对话服务
│   ├── chart_service.py    # 图表处理服务
│   ├── d3js_generator.py   # D3.js配置生成
│   ├── markdown_to_typst_service.py # Typst转换服务
│   └── ...
├── rust-tools/             # Rust工具链
│   ├── cmarker-typst/      # Rust核心转换引擎
│   └── extract-tool/       # Python绑定
├── wheels/                 # 预构建二进制轮子
│   ├── linux-x86_64/       # Linux轮子
│   └── win-x86_64/         # Windows轮子
├── assets/template/        # Typst模板文件
├── fonts/                  # 字体资源 (中文支持)
├── web/                    # Next.js前端
│   ├── src/app/           # App Router页面
│   │   └── typst-demo/    # Typst预览页面
│   ├── src/components/    # React组件
│   │   ├── ai-editor/     # AI编辑器组件
│   │   └── TypstCoreViewer.tsx # Typst预览组件
│   ├── src/lib/           # 工具库
│   └── src/hooks/         # React Hooks
├── scripts/               # 测试和工具脚本
│   ├── test_markdown_to_typst.py # Typst转换测试
│   └── ...
└── models/                # 数据模型定义
```
