import { Chart, DocumentTemplate, WordParseResult } from '../types/chart'
import { chartGenerator } from './chart-generator'

// Re-export types for backward compatibility
export type { Chart, DocumentTemplate, WordParseResult } from '../types/chart'

const API_BASE_URL = process.env.NEXT_PUBLIC_AI_BACKEND_URL || 'http://localhost:8000'
const NEXT_API_BASE_URL = '' // Use Next.js API routes for proxy endpoints

// Authentication types
export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
}

export interface User {
  id: number
  email: string
  username: string
  full_name?: string
  role: string
  is_active: boolean
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  // Determine which base URL to use based on endpoint
  private getApiUrl(endpoint: string): string {
    // Auth endpoints go directly to backend
    if (endpoint.startsWith('/api/v1/auth/')) {
      return `${this.baseURL}${endpoint}`
    }
    // All other endpoints go through Next.js API routes
    // Remove the /api prefix since we're adding it back
    const cleanEndpoint = endpoint.replace(/^\/api/, '')
    return `${NEXT_API_BASE_URL}/api${cleanEndpoint}`
  }

  // 基础请求方法
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = this.getApiUrl(endpoint)
    
    const defaultOptions: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
    
    const config = { ...defaultOptions, ...options }
    
    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // 文件上传方法
  private async upload<T>(endpoint: string, file: File): Promise<T> {
    const url = this.getApiUrl(endpoint)
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {},
      })

      if (!response.ok) {
        throw new Error(`Upload failed! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('File upload failed:', error)
      throw error
    }
  }

  // 上传并处理Word文档
  async uploadAndProcessDocument(file: File): Promise<WordParseResult> {
    return this.upload<WordParseResult>('/api/word-format/upload-d3', file)
  }

  // 将Chart JSON配置转换为SVG字符串
  async generateChartSVG(chartConfig: Chart): Promise<string> {
    return chartGenerator.generateChartSVG(chartConfig)
  }

  // 获取所有文档模板
  async getDocuments(): Promise<DocumentTemplate[]> {
    return this.request<DocumentTemplate[]>('/api/word-format/documents')
  }

  // 获取文档详细内容
  async getDocumentContent(docId: number): Promise<DocumentTemplate> {
    return this.request<DocumentTemplate>(`/api/word-format/documents/${docId}`)
  }

  // 删除文档模板
  async deleteDocumentTemplate(templateId: number): Promise<{ success: boolean; message?: string }> {
    return this.request(`/api/word-format/documents/${templateId}`, {
      method: 'DELETE'
    })
  }

  // 导出Word文档
  async exportWord(markdown: string): Promise<Blob> {
    const url = this.getApiUrl('/word-format/export-word')
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ markdown })
    })

    if (!response.ok) {
      throw new Error(`Export failed! status: ${response.status}`)
    }

    return response.blob()
  }

  // 导出PDF文档  
  async exportPdf(markdown: string): Promise<Blob> {
    const url = this.getApiUrl('/word-format/export-pdf')
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ markdown })
    })

    if (!response.ok) {
      throw new Error(`PDF export failed! status: ${response.status}`)
    }

    return response.blob()
  }

  // 删除文档（删除接口）
  async deleteDocument(docId: number): Promise<{ success: boolean; message?: string }> {
    return this.request(`/api/word-format/documents/${docId}`, {
      method: 'DELETE'
    })
  }

  // 获取模板列表 (别名方法，保持向后兼容)
  async getTemplates(): Promise<DocumentTemplate[]> {
    return this.getDocuments()
  }

  // Authentication methods
  async login(email: string, password: string): Promise<LoginResponse> {
    const response = await fetch(`/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `Login failed! status: ${response.status}`)
    }

    const data = await response.json()
    // Tokens are managed via HttpOnly cookies by the server route
    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async signup(_email: string, _password: string, _fullName?: string): Promise<User> {
    // Note: This endpoint is not implemented yet on backend
    throw new Error('Registration is not available yet')
  }

  async refreshToken(): Promise<LoginResponse> {
    const response = await fetch(`/api/auth/refresh`, { method: 'POST' })
    if (!response.ok) {
      throw new Error('Token refresh failed')
    }
    return response.json()
  }

  async logout(): Promise<void> {
    await fetch(`/api/auth/logout`, { method: 'POST' })
  }

  getAccessToken(): string | null {
    // HttpOnly cookie; not available to JS
    return null
  }

  isAuthenticated(): boolean {
    // With HttpOnly cookies, client cannot determine; call a session endpoint if needed
    return false
  }

  // 完整的 Typst 编译方法（Markdown -> Typst -> Vector）
  async compileMarkdownToVector(markdown: string, useConversion: boolean = true): Promise<Blob> {
    const url = this.getApiUrl('/api/typst-compile/compile-full')
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(typeof window !== 'undefined' && localStorage.getItem('access_token')
          ? { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
          : {}),
      },
      body: JSON.stringify({
        markdown: markdown,
        use_conversion: useConversion
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Typst 编译失败! status: ${response.status}, error: ${errorText}`)
    }

    return response.blob()
  }

  // 测试 Markdown 转换（仅返回 Typst 内容）
  async testMarkdownConversion(markdown: string): Promise<{typst_content: string, content_length: number}> {
    const url = this.getApiUrl('/api/typst-compile/test-conversion')
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(typeof window !== 'undefined' && localStorage.getItem('access_token')
          ? { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
          : {}),
      },
      body: JSON.stringify({
        markdown: markdown,
        use_conversion: true
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Markdown 转换失败! status: ${response.status}, error: ${errorText}`)
    }

    const result = await response.json()
    return result.data
  }
}

export const apiClient = new ApiClient()
