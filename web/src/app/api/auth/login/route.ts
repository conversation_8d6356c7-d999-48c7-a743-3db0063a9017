import { NextRequest, NextResponse } from 'next/server'
import { setAuthCookies } from '@/lib/server-auth'

const BACKEND_URL = process.env.NEXT_PUBLIC_AI_BACKEND_URL || 'http://localhost:8000'

// Proxy login to FastAPI using OAuth2PasswordRequestForm (username/password)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => null)

    if (!body || typeof body.email !== 'string' || typeof body.password !== 'string') {
      return NextResponse.json(
        { error: 'Invalid payload. Expect { email, password }' },
        { status: 400 }
      )
    }

    const form = new URLSearchParams()
    form.set('username', body.email)
    form.set('password', body.password)

    const backendResp = await fetch(`${BACKEND_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: form.toString(),
    })

    if (!backendResp.ok) {
      const err = await backendResp.json().catch(async () => ({ detail: await backendResp.text() }))
      return NextResponse.json(
        { error: 'Login failed', detail: err?.detail || backendResp.statusText },
        { status: backendResp.status }
      )
    }

    const data = await backendResp.json()

    // Set HttpOnly cookies for access and refresh tokens
    const res = NextResponse.json(data, { status: 200 })
    setAuthCookies(res, data.access_token, data.refresh_token, data.expires_in)
    return res
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json({ error: 'Proxy error', detail: message }, { status: 500 })
  }
}
