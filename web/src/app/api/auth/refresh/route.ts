import { NextRequest, NextResponse } from 'next/server'
import { setAuthCookies } from '@/lib/server-auth'

const BACKEND_URL = process.env.NEXT_PUBLIC_AI_BACKEND_URL || 'http://localhost:8000'

export async function POST(request: NextRequest) {
  try {
    const refreshToken = request.cookies.get('refresh_token')?.value
    if (!refreshToken) {
      return NextResponse.json({ error: 'No refresh token' }, { status: 401 })
    }

    // Backend expects refresh_token as a query param (per FastAPI signature)
    const backendResp = await fetch(
      `${BACKEND_URL}/api/v1/auth/refresh?refresh_token=${encodeURIComponent(refreshToken)}`,
      { method: 'POST' }
    )

    if (!backendResp.ok) {
      const err = await backendResp.json().catch(async () => ({ detail: await backendResp.text() }))
      return NextResponse.json(
        { error: 'Refresh failed', detail: err?.detail || backendResp.statusText },
        { status: backendResp.status }
      )
    }

    const data = await backendResp.json()
    const res = NextResponse.json(data, { status: 200 })
    setAuthCookies(res, data.access_token, data.refresh_token, data.expires_in)
    return res
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json({ error: 'Proxy error', detail: message }, { status: 500 })
  }
}

