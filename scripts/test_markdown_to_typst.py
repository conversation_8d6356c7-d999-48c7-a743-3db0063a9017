"""
测试Markdown转Typst服务
"""

import asyncio
import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from services.markdown_to_typst_service import MarkdownToTypstService


async def test_markdown_to_typst():
    """测试Markdown转Typst服务"""
    print("开始测试Markdown转Typst服务...")

    # 创建测试服务实例
    service = MarkdownToTypstService()

    try:
        # 使用绝对路径确保正确性
        project_root = Path(__file__).parent.parent
        input_file_path = project_root / "tmp" / "typst"  / "test.md"
        output_file_path = project_root / "tmp" / "typst"  / "test.typ"
        input_file_path = str(input_file_path)
        output_file_path = str(output_file_path)

        # 确保输入文件存在
        if not os.path.exists(input_file_path):
            print(f"错误: 输入文件不存在: {input_file_path}")
            return

        # 确保输出目录存在
        output_dir = os.path.dirname(output_file_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # 测试文件转换
        print(f"测试文件转换: {input_file_path}")
        output_path = await service.convert_file_async(input_file_path, output_file_path)
        print(f"文件转换成功，输出路径: {output_path}")

        # 读取并显示输出文件内容
        with open(output_path, "r", encoding="utf-8") as f:
            output_content = f.read()
        print("输出文件内容预览:")
        print(output_content[:100] + ("..." if len(output_content) > 500 else ""))

        print("测试完成！")
    except Exception as e:
        print(f"测试失败: {str(e)}")
    finally:
        # 不需要删除输出文件，用户希望保留
        pass


if __name__ == "__main__":
    asyncio.run(test_markdown_to_typst())