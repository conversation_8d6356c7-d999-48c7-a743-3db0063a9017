from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Dict, Any
import json
import logging
import os
from pathlib import Path
import time
from datetime import datetime

from services.ai_chat_service import AIChatService
from config import settings
from core.security import get_current_user, require_roles
from models.database import User

router = APIRouter(prefix="/api", tags=["reports"])
logger = logging.getLogger(__name__)

# 初始化服务
ai_chat_service = AIChatService()


@router.post("/ai-chat")
async def chat_with_ai(request: Dict[str, Any], current_user: User = Depends(get_current_user)):
    """AI对话接口"""
    try:
        message = request.get("message")
        context = request.get("context", {})
        
        if not message:
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        # 处理AI对话
        response = await ai_chat_service.process_user_message(message, context)
        
        return {
            "success": True,
            "message": response.get("message", ""),
            "type": response.get("type", ""),
            "suggestion": response.get("suggestion"),
            "action": response.get("action")
        }
        
    except Exception as e:
        logger.error(f"AI对话处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI对话失败: {str(e)}")


@router.post("/reports/export/{format}")
async def export_report(format: str, request: Dict[str, Any], current_user: User = Depends(get_current_user)):
    """导出报告"""
    try:
        # Note: content and template are currently unused in mock implementation
        # content = request.get("content", "")
        # template = request.get("template", {})
        
        if format not in ["word", "pdf"]:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
        
        # 这里应该实现实际的导出逻辑
        # 将Markdown内容转换为Word或PDF
        
        # 暂时返回成功响应
        return {"success": True, "message": f"报告已导出为{format}格式"}
        
    except Exception as e:
        logger.error(f"报告导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"报告导出失败: {str(e)}")


@router.get("/charts/{chart_id}_config.json")
async def get_chart_config(chart_id: str):
    """获取图表配置文件"""
    try:
        # 构建配置文件路径
        config_dir = Path("./output/chartjs_configs")
        config_file = config_dir / f"{chart_id}_config.json"
        
        # 检查文件是否存在
        if not config_file.exists():
            raise HTTPException(status_code=404, detail=f"图表配置文件未找到: {chart_id}")
        
        # 读取并返回JSON配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return JSONResponse(content=config_data)
        
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail=f"图表配置文件未找到: {chart_id}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail=f"图表配置文件格式错误: {chart_id}")
    except Exception as e:
        logger.error(f"获取图表配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图表配置失败: {str(e)}")

@router.get("/charts/configs")
async def list_chart_configs():
    """获取所有可用的图表配置文件列表"""
    try:
        config_dir = Path("./output/chartjs_configs")
        
        if not config_dir.exists():
            return {"success": True, "charts": []}
        
        chart_configs = []
        for config_file in config_dir.glob("chart_*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                chart_id = config_file.stem.replace('_config', '')
                chart_configs.append({
                    "id": chart_id,
                    "title": config_data.get("options", {}).get("plugins", {}).get("title", {}).get("text", chart_id),
                    "type": config_data.get("type", "unknown"),
                    "configFile": f"/api/charts/{config_file.name}",
                    "lastModified": os.path.getmtime(config_file)
                })
                
            except Exception as e:
                logger.warning(f"跳过无效配置文件 {config_file}: {str(e)}")
                continue
        
        return {"success": True, "charts": chart_configs}
        
    except Exception as e:
        logger.error(f"获取图表配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图表配置列表失败: {str(e)}")

