"""
PDF解析API路由
"""

import shutil
from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from typing import Dict, Any
from core.logging import get_logger
from services.pdf_parser import CozePDFService
from pathlib import Path
from fastapi.staticfiles import StaticFiles

logger = get_logger(__name__)
router = APIRouter(
    prefix="/api/pdf",
    tags=["PDF Processing"],
)
pdf_service = CozePDFService()


# 项目根目录
project_root = Path(__file__).parent.parent

# 确保静态文件目录存在
results_dir = project_root / "tmp" / "results"
results_dir.mkdir(parents=True, exist_ok=True)

# 挂载静态文件目录
router.mount("/results", StaticFiles(directory=str(results_dir)), name="results")

@router.post("/upload-pdf")
async def upload_pdf(file: UploadFile = File(...)):
    """
    上传PDF文件并解析为Markdown
    
    Args:
        file: PDF文件
        
    Returns:
        解析后的Markdown内容和元数据
    """
    try:
        # 验证文件类型
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail="只支持PDF文件格式"
            )
        
        # 创建上传目录（如果不存在）
        tmp_dir = Path("../tmp/pdf_uploads")

        tmp_dir.mkdir(parents=True, exist_ok=True)
        file_path = tmp_dir / file.filename
        
        # 保存上传的PDF文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 创建结果目录
        output_dir = Path("../tmp/results")
        pdf_filename = Path(file.filename).stem
        result_dir = output_dir / pdf_filename
        result_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 调用PDF解析服务 - 无需手动创建结果目录
            result = await pdf_service.parse_pdf_file(str(file_path))
            
            response_data = {
                "success": True,
                "message": "PDF解析成功",
                "data": {
                    "markdown_content": result.markdown_content,
                    "parsed_array": result.parsed_array,
                    "possible_titles": result.possible_titles,
                    "content_length": len(result.markdown_content),
                    "file_name": file.filename,
                    "result_url": f"/api/pdf/{pdf_filename}/{pdf_filename}.md"
                }
            }
            
            return JSONResponse(content=response_data)
            
        finally:
            # 清理临时文件
            if file_path.exists():
                file_path.unlink()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF解析失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"PDF解析失败: {str(e)}"
        )
