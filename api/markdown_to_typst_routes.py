"""
Markdown转Typst API路由

/save-markdown-content
/get-markdown-content
这两个后续可以优化。(save方便对比转换前后的内容)

"""

from fastapi import APIRouter, HTTPException, Body, Path, Depends
from fastapi.responses import JSONResponse, Response
from typing import Dict, Any
from core.logging import get_logger
from services.markdown_to_typst_service import MarkdownToTypstService
from pathlib import Path
import os
from core.security import get_current_user
from models.database import User
from fastapi.responses import JSONResponse
from typing import Dict, Any
from core.logging import get_logger
from services.markdown_to_typst_service import MarkdownToTypstService
from pathlib import Path
import os

logger = get_logger(__name__)
router = APIRouter(
    prefix="/api/markdown-to-typst",
    tags=["Markdown to Typst"],
)
markdown_to_typst_service = MarkdownToTypstService()

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

@router.post("/save-markdown-content")
async def save_markdown_content(
    content: str = Body(..., embed=True),
    current_user: User = Depends(get_current_user)
):
    """
    保存用户编辑后的Markdown内容

    Args:
        content: Markdown内容
        current_user: 当前登录用户

    Returns:
        保存结果信息
    """
    try:
        logger.info(f"开始保存用户 {current_user.username} 的Markdown内容，长度: {len(content)}")

        # 构建用户目录路径
        user_dir = PROJECT_ROOT / "tmp" / "results" / current_user.username
        # 确保用户目录存在
        user_dir.mkdir(parents=True, exist_ok=True)

        # 构建图片目录路径
        images_dir = user_dir / "images"
        images_dir.mkdir(parents=True, exist_ok=True)

        # 保存Markdown文件
        md_file_path = user_dir / f"{current_user.username}.md"
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        response_data = {
            "success": True,
            "message": "Markdown内容保存成功",
            "data": {
                "file_path": str(md_file_path),
                "content_length": len(content),
                "username": current_user.username
            }
        }

        return JSONResponse(content=response_data)

    except Exception as e:
        logger.error(f"保存Markdown内容失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"保存Markdown内容失败: {str(e)}"
        )

@router.post("/convert-markdown-by-username/{username}")
async def convert_markdown_by_username(
    username: str = Path(default=..., description="用户名"),
    current_user: User = Depends(get_current_user)
):
    """
    根据用户名转换Markdown为Typst格式

    Args:
        username: 用户名，对应results下的文件夹名称
        current_user: 当前登录用户

    Returns:
        转换后的Typst内容
    """
    try:
        # 确保当前用户只能转换自己的文档
        if current_user.username != username:
            raise HTTPException(
                status_code=403,
                detail="无权访问他人文档"
            )

        logger.info(f"开始转换用户 {username} 的Markdown到Typst")

        # 构建Markdown文件路径
        md_file_path = PROJECT_ROOT / "tmp" / "results" / username / f"{username}.md"
        typst_file_path = PROJECT_ROOT / "tmp" / "results" / username / f"{username}.typ"

        # 检查Markdown文件是否存在
        if not os.path.exists(md_file_path):
            raise HTTPException(
                status_code=404,
                detail=f"找不到用户 {username} 的Markdown文件: {md_file_path}"
            )

        # 确保输出目录存在
        output_dir = os.path.dirname(typst_file_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # 调用转换服务
        await markdown_to_typst_service.convert_file_async(
            str(md_file_path),
            str(typst_file_path)
        )

        # 读取Typst内容
        with open(typst_file_path, 'r', encoding='utf-8') as f:
            typst_content = f.read()

        response_data = {
            "success": True,
            "message": "Markdown转换Typst成功",
            "data": {
                "typst_content": typst_content,
                "content_length": len(typst_content),
                "username": username
            }
        }

        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Markdown转换Typst失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Markdown转换Typst失败: {str(e)}"
        )

@router.get("/get-markdown-content/{username}")
async def get_markdown_content(
    username: str = Path(default=..., description="用户名"),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的Markdown文件内容

    Args:
        username: 用户名
        current_user: 当前登录用户

    Returns:
        Markdown文件内容（纯文本格式）
    """
    try:
        # 确保当前用户只能获取自己的文档
        if current_user.username != username:
            raise HTTPException(
                status_code=403,
                detail="无权访问他人文档"
            )

        logger.info(f"开始获取用户 {username} 的Markdown文件内容")

        # 构建Markdown文件路径
        md_file_path = PROJECT_ROOT / "tmp" / "results" / username / f"{username}.md"

        # 检查Markdown文件是否存在
        if not os.path.exists(md_file_path):
            raise HTTPException(
                status_code=404,
                detail=f"找不到用户 {username} 的Markdown文件: {md_file_path}"
            )

        # 读取Markdown内容
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()

        return Response(content=md_content, media_type="text/plain")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Markdown文件内容失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取Markdown文件内容失败: {str(e)}"
        )

@router.get("/get-typst-content/{username}")
async def get_typst_content(
    username: str = Path(default=..., description="用户名"),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的Typst文件内容

    Args:
        username: 用户名
        current_user: 当前登录用户

    Returns:
        Typst文件内容（纯文本格式）
    """
    try:
        # 确保当前用户只能获取自己的文档
        if current_user.username != username:
            raise HTTPException(
                status_code=403,
                detail="无权访问他人文档"
            )

        logger.info(f"开始获取用户 {username} 的Typst文件内容")

        # 构建Typst文件路径
        typst_file_path = PROJECT_ROOT / "tmp" / "results" / username / f"{username}.typ"

        # 检查Typst文件是否存在
        if not os.path.exists(typst_file_path):
            raise HTTPException(
                status_code=404,
                detail=f"找不到用户 {username} 的Typst文件: {typst_file_path}"
            )

        # 读取Typst内容
        with open(typst_file_path, 'r', encoding='utf-8') as f:
            typst_content = f.read()

        return Response(content=typst_content, media_type="text/plain")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Typst文件内容失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取Typst文件内容失败: {str(e)}"
        )

@router.post("/convert-markdown-content")
async def convert_markdown_content(content: str = Body(..., embed=True)):
    """
    转换用户编辑好的Markdown内容为Typst格式

    Args:
        content: Markdown内容

    Returns:
        转换后的Typst内容
    """
    try:
        logger.info(f"开始转换Markdown内容，长度: {len(content)}")

        # 调用转换服务
        typst_content = markdown_to_typst_service.convert_markdown_to_typst(content)

        response_data = {
            "success": True,
            "message": "Markdown内容转换成功",
            "data": {
                "typst_content": typst_content,
                "content_length": len(typst_content)
            }
        }

        return JSONResponse(content=response_data)

    except Exception as e:
        logger.error(f"Markdown内容转换失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Markdown内容转换失败: {str(e)}"
        )