"""
AI Streaming Routes
Handles streaming AI text processing requests
"""

import asyncio
import json
from typing import AsyncGenerator
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import StreamingResponse, Response
from pydantic import BaseModel, Field
import logging

from services.ai_chat_service import AIChatService
from config import get_settings
from core.security import get_current_user, require_roles, is_client_disconnected
from models.database import User
from core.rate_limit import limit_requests

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/prose", tags=["AI Streaming"])

class AIStreamRequest(BaseModel):
    """AI streaming request model - optimized with minimal parameters"""
    prompt: str = Field(..., description="The complete AI prompt with context already included")
    context: str = Field(default="", description="Full document context for AI understanding")  
    selected_text: str = Field(default="", description="Currently selected text")

class AIStreamService:
    """Service for handling AI streaming responses"""
    
    def __init__(self):
        self.ai_chat_service = AIChatService()
        self.settings = get_settings()
    
    async def process_stream_request(self, request_model: AIStreamRequest, request: Request) -> AsyncGenerator[str, None]:
        """
        Process streaming AI request
        
        Args:
            request: The AI stream request
            
        Yields:
            Server-Sent Events formatted strings
        """
        try:
            # Direct streaming processing - frontend handles prompt formatting
            async for chunk in self._stream_ai_response(request_model.prompt, request):
                # 客户端断开则停止
                if await is_client_disconnected(request):
                    logger.info("Client disconnected, stopping stream")
                    break
                yield chunk
                    
        except Exception as e:
            logger.error(f"Error in stream processing: {str(e)}", exc_info=True)
            yield self._format_sse_error(f"Processing error: {str(e)}")
    
    async def _stream_ai_response(
        self, 
        prompt: str,
        request: Request,
    ) -> AsyncGenerator[str, None]:
        """Stream AI response using the chat service with real-time chunks"""
        
        try:
            # Get streaming response from OpenAI
            from openai import AsyncOpenAI
            
            client = AsyncOpenAI(
                api_key=self.settings.llm.api_key,
                base_url=self.settings.llm.base_url
            )
            
            logger.info(f"Starting AI streaming for prompt length: {len(prompt)}")
            
            # Create streaming completion
            stream = await client.chat.completions.create(
                model=self.settings.llm.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional writing assistant. Help users improve their text content with clear, concise, and well-structured responses. Always maintain the document's original format and style when making improvements."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=self.settings.llm.temperature,
                max_tokens=self.settings.llm.max_tokens,
                stream=True
            )
            
            # Track streaming progress
            total_content = ""
            chunk_count = 0
            
            # Stream the response with proper chunking
            async for chunk in stream:
                # 客户端断开则退出
                if await is_client_disconnected(request):
                    break
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    total_content += content
                    chunk_count += 1
                    
                    # Send each chunk immediately for real-time display
                    yield self._format_sse_data(content)
                    
                    # Add small delay for better streaming visualization
                    await asyncio.sleep(0.01)
            
            logger.info(f"AI streaming completed: {chunk_count} chunks, {len(total_content)} characters")
            
            # Send completion signal
            yield self._format_sse_event("complete", "")
            
        except Exception as e:
            logger.error(f"Error in AI streaming: {str(e)}", exc_info=True)
            yield self._format_sse_error(f"AI streaming error: {str(e)}")
    
    async def _stream_text(self, text: str) -> AsyncGenerator[str, None]:
        """Stream text content in small chunks for better UX"""
        
        # Split text into smaller chunks (words or short phrases)
        import re
        
        # Split by sentences first, then by words for more granular streaming
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        for sentence in sentences:
            if sentence.strip():
                # Further split sentence into words for smoother streaming
                words = sentence.split()
                word_buffer = ""
                
                for i, word in enumerate(words):
                    word_buffer += word + " "
                    
                    # Send chunks of 3-5 words for smooth streaming
                    if (i + 1) % 4 == 0 or i == len(words) - 1:
                        if word_buffer.strip():
                            yield self._format_sse_data(word_buffer)
                            word_buffer = ""
                            # Small delay for visual streaming effect
                            await asyncio.sleep(0.08)
        
        # Send completion signal
        yield self._format_sse_event("complete", "")
    
    def _format_sse_data(self, data: str) -> str:
        """Format data as Server-Sent Event"""
        return f"event: message\ndata: {data}\n\n"
    
    def _format_sse_event(self, event: str, data: str) -> str:
        """Format event as Server-Sent Event"""
        return f"event: {event}\ndata: {data}\n\n"
    
    def _format_sse_error(self, error: str) -> str:
        """Format error as Server-Sent Event"""
        return f"event: error\ndata: {error}\n\n"

# Service instance
ai_stream_service = AIStreamService()

@router.post("/generate", dependencies=[Depends(limit_requests(60, 60))])
async def generate_ai_content(
    request_model: AIStreamRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
):
    """
    Generate AI content with streaming response
    
    This endpoint processes AI requests and returns streaming responses
    compatible with Server-Sent Events (SSE) format.
    """
    try:
        # Validate request
        if not request_model.prompt.strip():
            raise HTTPException(status_code=400, detail="Prompt cannot be empty")
        
        user_id = getattr(current_user, 'id', None)
        logger.info(f"Processing AI stream request: prompt_length={len(request_model.prompt)}", extra={'user_id': user_id})        
        # Return streaming response
        return StreamingResponse(
            ai_stream_service.process_stream_request(request_model, request),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache, no-transform",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
            },
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_ai_content: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.options("/generate")
async def generate_ai_content_options() -> Response:
    """CORS 预检由全局中间件处理，这里返回 200"""
    return Response(status_code=200)

@router.get("/health")
async def health_check():
    """Health check endpoint for AI streaming service"""
    try:
        # Quick test of AI service
        settings = get_settings()
        return {
            "status": "healthy",
            "ai_model": settings.llm.model_name,
            "base_url": settings.llm.base_url,
            "timestamp": "now"
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")